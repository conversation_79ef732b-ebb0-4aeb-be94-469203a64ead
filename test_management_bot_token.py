#!/usr/bin/env python
"""
Test script to verify the management bot token and configuration.
"""

import sys
import os
import telebot
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_management_bot():
    """Test the management bot token and configuration"""
    try:
        # Test environment variable loading
        from src.config import MANAGEMENT_BOT_TOKEN
        print(f"✅ MANAGEMENT_BOT_TOKEN loaded: {bool(MANAGEMENT_BOT_TOKEN)}")
        
        if MANAGEMENT_BOT_TOKEN:
            print(f"✅ Token preview: {MANAGEMENT_BOT_TOKEN[:10]}...")
            
            # Test bot connection
            bot = telebot.TeleBot(MANAGEMENT_BOT_TOKEN)
            bot_info = bot.get_me()
            print(f"✅ Management bot connected successfully: @{bot_info.username}")
            print(f"Bot ID: {bot_info.id}")
            print(f"Bot Name: {bot_info.first_name}")
            
            # Test bot instance import
            from src.bot_instance import management_bot
            print("✅ Management bot instance imported successfully")
            
            # Test management bot module import
            from src.bots.management_bot import get_management_bot
            mgmt_bot = get_management_bot()
            print("✅ Management bot module imported successfully")
            
            return True
        else:
            print("❌ MANAGEMENT_BOT_TOKEN not found in environment")
            return False
            
    except Exception as e:
        print(f"❌ Error testing management bot: {e}")
        return False

if __name__ == "__main__":
    print("Testing Management Bot Configuration...")
    print("=" * 50)
    success = test_management_bot()
    if success:
        print("\n🎉 All management bot tests passed!")
    else:
        print("\n❌ Management bot tests failed!")
    sys.exit(0 if success else 1)
