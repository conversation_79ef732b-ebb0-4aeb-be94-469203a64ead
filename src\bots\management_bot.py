"""
Management Bot for Wiz Aroma Delivery System
Comprehensive management interface for delivery personnel, analytics, and system oversight.
Replaces the previous notification bot with full management capabilities.
Access restricted to authorized management Telegram IDs.
"""

import telebot
from telebot import types
import datetime
from typing import Dict, List, Any, Optional
import logging
import sys
import os

from src.config import logger
from src.firebase_db import get_data, set_data, delete_data
from src.data_models import (
    delivery_personnel,
    delivery_personnel_assignments,
    delivery_personnel_availability,
    delivery_personnel_capacity,
    delivery_personnel_zones,
    delivery_personnel_performance,
    DeliveryPersonnel
)
from src.utils.delivery_personnel_utils import (
    create_delivery_personnel,
    verify_delivery_personnel,
    get_delivery_personnel_by_telegram_id,
    get_all_delivery_personnel,
    remove_delivery_personnel
)

# Management Bot Configuration
MANAGEMENT_BOT_AUTHORIZED_IDS = [7729984017]  # Authorized management user IDs

# Get management bot instance from bot_instance.py to avoid circular imports
def get_management_bot():
    """Get the management bot instance"""
    from src.bot_instance import notification_bot
    return notification_bot

management_bot = get_management_bot()

def is_authorized_user(user_id: int) -> bool:
    """Check if user is authorized to access management functions"""
    return user_id in MANAGEMENT_BOT_AUTHORIZED_IDS

def create_main_menu_keyboard():
    """Create the main management menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    
    # Delivery Personnel Management
    keyboard.add(
        types.InlineKeyboardButton("👥 Personnel Management", callback_data="mgmt_personnel"),
        types.InlineKeyboardButton("📊 Analytics Dashboard", callback_data="mgmt_analytics")
    )
    
    # System Management
    keyboard.add(
        types.InlineKeyboardButton("📈 Reports", callback_data="mgmt_reports"),
        types.InlineKeyboardButton("💰 Earnings", callback_data="mgmt_earnings")
    )
    
    # Utilities
    keyboard.add(
        types.InlineKeyboardButton("🔄 Refresh Data", callback_data="mgmt_refresh"),
        types.InlineKeyboardButton("ℹ️ System Info", callback_data="mgmt_info")
    )
    
    return keyboard

def create_personnel_menu_keyboard():
    """Create personnel management menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    
    keyboard.add(
        types.InlineKeyboardButton("➕ Add Personnel", callback_data="pers_add"),
        types.InlineKeyboardButton("➖ Remove Personnel", callback_data="pers_remove")
    )
    keyboard.add(
        types.InlineKeyboardButton("📋 List All", callback_data="pers_list"),
        types.InlineKeyboardButton("🔍 Search", callback_data="pers_search")
    )
    keyboard.add(
        types.InlineKeyboardButton("📊 Performance", callback_data="pers_performance"),
        types.InlineKeyboardButton("🔧 Manage Status", callback_data="pers_status")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
    )
    
    return keyboard

def create_analytics_menu_keyboard():
    """Create analytics dashboard menu keyboard"""
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    
    keyboard.add(
        types.InlineKeyboardButton("📅 Daily Summary", callback_data="analytics_daily"),
        types.InlineKeyboardButton("📊 Weekly Summary", callback_data="analytics_weekly")
    )
    keyboard.add(
        types.InlineKeyboardButton("📈 Monthly Summary", callback_data="analytics_monthly"),
        types.InlineKeyboardButton("🔢 Transaction Counts", callback_data="analytics_transactions")
    )
    keyboard.add(
        types.InlineKeyboardButton("🚚 Delivery Stats", callback_data="analytics_delivery"),
        types.InlineKeyboardButton("💹 Trend Analysis", callback_data="analytics_trends")
    )
    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Main", callback_data="mgmt_main")
    )
    
    return keyboard

def handle_start(message):
    """Handle start command and show main menu"""
    user_id = message.from_user.id
    
    if not is_authorized_user(user_id):
        management_bot.reply_to(
            message,
            "❌ **Access Denied**\n\n"
            "You are not authorized to access the Management Bot.\n"
            "Contact system administrator for access.",
            parse_mode='Markdown'
        )
        return
    
    welcome_text = f"""
🏢 **Wiz Aroma Management Bot**

Welcome to the comprehensive management interface!

**Available Functions:**
👥 **Personnel Management** - Add/remove delivery personnel
📊 **Analytics Dashboard** - View system performance metrics
📈 **Reports** - Generate detailed reports
💰 **Earnings** - Track delivery personnel earnings
🔄 **Data Management** - Refresh and maintain system data

Select an option below to get started:
    """
    
    management_bot.send_message(
        message.chat.id,
        welcome_text,
        reply_markup=create_main_menu_keyboard(),
        parse_mode='Markdown'
    )

def handle_callback_query(call):
    """Handle all callback queries"""
    user_id = call.from_user.id
    
    if not is_authorized_user(user_id):
        management_bot.answer_callback_query(
            call.id,
            "❌ Access denied. You are not authorized to use this bot.",
            show_alert=True
        )
        return
    
    try:
        # Main menu navigation
        if call.data == "mgmt_main":
            show_main_menu(call)
        elif call.data == "mgmt_personnel":
            show_personnel_menu(call)
        elif call.data == "mgmt_analytics":
            show_analytics_menu(call)
        elif call.data == "mgmt_reports":
            show_reports_menu(call)
        elif call.data == "mgmt_earnings":
            show_earnings_menu(call)
        elif call.data == "mgmt_refresh":
            refresh_system_data(call)
        elif call.data == "mgmt_info":
            show_system_info(call)
        
        # Personnel management
        elif call.data.startswith("pers_"):
            handle_personnel_action(call)
        
        # Analytics
        elif call.data.startswith("analytics_"):
            handle_analytics_action(call)
        
        # Reports
        elif call.data.startswith("reports_"):
            handle_reports_action(call)
        
        # Earnings
        elif call.data.startswith("earnings_"):
            handle_earnings_action(call)

        # Personnel removal confirmation
        elif call.data.startswith("remove_personnel_"):
            handle_personnel_removal(call)
        elif call.data.startswith("confirm_remove_"):
            confirm_personnel_removal(call)

        else:
            management_bot.answer_callback_query(
                call.id,
                "❓ Unknown action. Please try again.",
                show_alert=True
            )
    
    except Exception as e:
        logger.error(f"Error handling callback query {call.data}: {e}")
        management_bot.answer_callback_query(
            call.id,
            "❌ An error occurred. Please try again.",
            show_alert=True
        )

def show_main_menu(call):
    """Show the main management menu"""
    welcome_text = """
🏢 **Wiz Aroma Management Bot**

**Management Dashboard**
Select a function to manage your delivery system:
    """
    
    management_bot.edit_message_text(
        welcome_text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=create_main_menu_keyboard(),
        parse_mode='Markdown'
    )

def show_personnel_menu(call):
    """Show personnel management menu"""
    # Get current personnel count
    personnel_data = get_data("delivery_personnel") or {}
    active_count = len([p for p in personnel_data.values() if p.get('status') != 'inactive'])
    
    text = f"""
👥 **Personnel Management**

**Current Status:**
• Active Personnel: {active_count}
• Total Registered: {len(personnel_data)}

**Management Options:**
    """
    
    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=create_personnel_menu_keyboard(),
        parse_mode='Markdown'
    )

def show_analytics_menu(call):
    """Show analytics dashboard menu"""
    text = """
📊 **Analytics Dashboard**

**Available Analytics:**
• Daily/Weekly/Monthly summaries
• Transaction counts and trends
• Delivery performance metrics
• System usage statistics

Select an analytics option:
    """
    
    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=create_analytics_menu_keyboard(),
        parse_mode='Markdown'
    )

# Placeholder functions for future implementation
def show_reports_menu(call):
    """Show reports menu - to be implemented"""
    management_bot.answer_callback_query(call.id, "📈 Reports feature coming soon!")

def show_earnings_menu(call):
    """Show earnings menu - to be implemented"""
    management_bot.answer_callback_query(call.id, "💰 Earnings feature coming soon!")

def refresh_system_data(call):
    """Refresh system data from Firebase"""
    management_bot.answer_callback_query(call.id, "🔄 Refreshing data...")
    # Implementation will be added in next phase

def show_system_info(call):
    """Show system information"""
    management_bot.answer_callback_query(call.id, "ℹ️ System info feature coming soon!")

def handle_personnel_action(call):
    """Handle personnel management actions"""
    action = call.data.replace("pers_", "")

    if action == "add":
        start_add_personnel(call)
    elif action == "remove":
        start_remove_personnel(call)
    elif action == "list":
        show_personnel_list(call)
    elif action == "search":
        start_personnel_search(call)
    elif action == "performance":
        show_personnel_performance(call)
    elif action == "status":
        show_personnel_status_management(call)
    else:
        management_bot.answer_callback_query(call.id, "❓ Unknown personnel action")

def handle_analytics_action(call):
    """Handle analytics actions"""
    action = call.data.replace("analytics_", "")

    if action == "daily":
        show_daily_analytics(call)
    elif action == "weekly":
        show_weekly_analytics(call)
    elif action == "monthly":
        show_monthly_analytics(call)
    elif action == "transactions":
        show_transaction_analytics(call)
    elif action == "delivery":
        show_delivery_analytics(call)
    elif action == "trends":
        show_trend_analytics(call)
    else:
        management_bot.answer_callback_query(call.id, "❓ Unknown analytics action")

def handle_reports_action(call):
    """Handle reports actions - to be implemented"""
    management_bot.answer_callback_query(call.id, "📈 Reports feature coming soon!")

def handle_earnings_action(call):
    """Handle earnings actions - to be implemented"""
    management_bot.answer_callback_query(call.id, "💰 Earnings feature coming soon!")

# Personnel Management Functions
def start_add_personnel(call):
    """Start the process of adding new delivery personnel"""
    text = """
➕ **Add New Delivery Personnel**

Please provide the following information in this format:

```
Name: [Full Name]
Phone: [Phone Number with country code]
Telegram ID: [Telegram User ID]
Areas: [Area1, Area2, Area3]
Vehicle: [motorcycle/bicycle/car/walking]
```

**Example:**
```
Name: John Doe
Phone: +251912345678
Telegram ID: 123456789
Areas: Federal, Piassa, Merkato
Vehicle: motorcycle
```

Reply to this message with the personnel information.
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        parse_mode='Markdown'
    )

    # Set up message handler for personnel data
    management_bot.register_next_step_handler(call.message, process_add_personnel)

def process_add_personnel(message):
    """Process the personnel addition request"""
    try:
        # Parse the personnel data
        lines = message.text.strip().split('\n')
        personnel_data = {}

        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip()
                personnel_data[key] = value

        # Validate required fields
        required_fields = ['name', 'phone', 'telegram id', 'areas', 'vehicle']
        missing_fields = [field for field in required_fields if field not in personnel_data]

        if missing_fields:
            management_bot.reply_to(
                message,
                f"❌ **Missing Information**\n\n"
                f"Please provide: {', '.join(missing_fields)}\n\n"
                f"Use the format shown in the previous message.",
                parse_mode='Markdown'
            )
            return

        # Parse areas
        areas = [area.strip() for area in personnel_data['areas'].split(',')]

        # Create delivery personnel
        personnel_id = create_delivery_personnel(
            name=personnel_data['name'],
            phone_number=personnel_data['phone'],
            service_areas=areas,
            telegram_id=personnel_data['telegram id'],
            vehicle_type=personnel_data['vehicle']
        )

        success_text = f"""
✅ **Personnel Added Successfully!**

**Details:**
• **ID:** {personnel_id}
• **Name:** {personnel_data['name']}
• **Phone:** {personnel_data['phone']}
• **Telegram ID:** {personnel_data['telegram id']}
• **Service Areas:** {', '.join(areas)}
• **Vehicle:** {personnel_data['vehicle']}

The personnel has been added to the system and is pending verification.
        """

        management_bot.reply_to(
            message,
            success_text,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error adding personnel: {e}")
        management_bot.reply_to(
            message,
            f"❌ **Error Adding Personnel**\n\n"
            f"An error occurred: {str(e)}\n\n"
            f"Please try again or contact system administrator.",
            parse_mode='Markdown'
        )

def start_remove_personnel(call):
    """Start the process of removing delivery personnel"""
    # Get all personnel
    personnel_data = get_data("delivery_personnel") or {}

    if not personnel_data:
        management_bot.edit_message_text(
            "❌ **No Personnel Found**\n\n"
            "There are no delivery personnel in the system to remove.",
            call.message.chat.id,
            call.message.message_id,
            parse_mode='Markdown'
        )
        return

    # Create keyboard with personnel list
    keyboard = types.InlineKeyboardMarkup(row_width=1)

    for personnel_id, person in personnel_data.items():
        name = person.get('name', 'Unknown')
        status = person.get('status', 'unknown')
        button_text = f"🗑️ {name} ({status})"
        keyboard.add(
            types.InlineKeyboardButton(
                button_text,
                callback_data=f"remove_personnel_{personnel_id}"
            )
        )

    keyboard.add(
        types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
    )

    text = """
➖ **Remove Delivery Personnel**

Select a personnel member to remove from the system:

⚠️ **Warning:** This action cannot be undone!
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def show_personnel_list(call):
    """Show list of all delivery personnel"""
    personnel_data = get_data("delivery_personnel") or {}

    if not personnel_data:
        text = "❌ **No Personnel Found**\n\nThere are no delivery personnel in the system."
    else:
        text = f"👥 **Delivery Personnel List**\n\n**Total Personnel:** {len(personnel_data)}\n\n"

        for personnel_id, person in personnel_data.items():
            name = person.get('name', 'Unknown')
            phone = person.get('phone_number', 'N/A')
            status = person.get('status', 'unknown')
            areas = person.get('service_areas', [])
            vehicle = person.get('vehicle_type', 'N/A')

            status_emoji = {
                'available': '✅',
                'busy': '🔄',
                'offline': '⭕',
                'inactive': '❌'
            }.get(status, '❓')

            text += f"""
**{status_emoji} {name}**
• ID: `{personnel_id}`
• Phone: {phone}
• Status: {status.title()}
• Vehicle: {vehicle.title()}
• Areas: {', '.join(areas) if areas else 'None'}
---
            """

    keyboard = types.InlineKeyboardMarkup()
    keyboard.add(
        types.InlineKeyboardButton("🔄 Refresh", callback_data="pers_list"),
        types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_personnel")
    )

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def start_personnel_search(call):
    """Start personnel search functionality"""
    management_bot.answer_callback_query(call.id, "🔍 Search feature coming soon!")

def show_personnel_performance(call):
    """Show personnel performance metrics"""
    management_bot.answer_callback_query(call.id, "📊 Performance metrics coming soon!")

def show_personnel_status_management(call):
    """Show personnel status management"""
    management_bot.answer_callback_query(call.id, "🔧 Status management coming soon!")

def handle_personnel_removal(call):
    """Handle personnel removal confirmation"""
    personnel_id = call.data.replace("remove_personnel_", "")

    # Get personnel details
    personnel_data = get_data("delivery_personnel") or {}
    person = personnel_data.get(personnel_id)

    if not person:
        management_bot.answer_callback_query(
            call.id,
            "❌ Personnel not found!",
            show_alert=True
        )
        return

    name = person.get('name', 'Unknown')
    phone = person.get('phone_number', 'N/A')
    status = person.get('status', 'unknown')

    # Create confirmation keyboard
    keyboard = types.InlineKeyboardMarkup(row_width=2)
    keyboard.add(
        types.InlineKeyboardButton("✅ Confirm Remove", callback_data=f"confirm_remove_{personnel_id}"),
        types.InlineKeyboardButton("❌ Cancel", callback_data="pers_remove")
    )

    text = f"""
⚠️ **Confirm Personnel Removal**

**Personnel Details:**
• **Name:** {name}
• **Phone:** {phone}
• **Status:** {status.title()}
• **ID:** `{personnel_id}`

**Warning:** This action will:
• Remove the personnel from the system
• Clear all their assignments
• Delete their performance history
• This action cannot be undone!

Are you sure you want to proceed?
    """

    management_bot.edit_message_text(
        text,
        call.message.chat.id,
        call.message.message_id,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )

def confirm_personnel_removal(call):
    """Confirm and execute personnel removal"""
    personnel_id = call.data.replace("confirm_remove_", "")

    try:
        # Get personnel details before removal
        personnel_data = get_data("delivery_personnel") or {}
        person = personnel_data.get(personnel_id)

        if not person:
            management_bot.answer_callback_query(
                call.id,
                "❌ Personnel not found!",
                show_alert=True
            )
            return

        name = person.get('name', 'Unknown')

        # Remove personnel using utility function
        success = remove_delivery_personnel(personnel_id)

        if success:
            text = f"""
✅ **Personnel Removed Successfully**

**{name}** has been removed from the system.

All associated data including:
• Personnel record
• Assignment history
• Performance metrics
• Availability status

Has been permanently deleted.
            """

            keyboard = types.InlineKeyboardMarkup()
            keyboard.add(
                types.InlineKeyboardButton("🔙 Back to Personnel", callback_data="mgmt_personnel")
            )

            management_bot.edit_message_text(
                text,
                call.message.chat.id,
                call.message.message_id,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )

            logger.info(f"Personnel {personnel_id} ({name}) removed successfully")

        else:
            management_bot.answer_callback_query(
                call.id,
                "❌ Failed to remove personnel. Please try again.",
                show_alert=True
            )

    except Exception as e:
        logger.error(f"Error removing personnel {personnel_id}: {e}")
        management_bot.answer_callback_query(
            call.id,
            f"❌ Error: {str(e)}",
            show_alert=True
        )

# Analytics Functions
def show_daily_analytics(call):
    """Show daily analytics summary"""
    try:
        today = datetime.datetime.now().strftime("%Y-%m-%d")

        # Get order data for today
        orders_data = get_data("orders") or {}
        today_orders = [
            order for order in orders_data.values()
            if order.get('timestamp', '').startswith(today)
        ]

        # Calculate metrics
        total_orders = len(today_orders)
        completed_orders = len([o for o in today_orders if o.get('status') == 'completed'])
        pending_orders = len([o for o in today_orders if o.get('status') in ['pending', 'processing']])

        # Calculate revenue (simplified - would need proper order totals)
        total_revenue = sum(
            float(order.get('total_amount', 0)) for order in today_orders
            if order.get('status') == 'completed'
        )

        # Get delivery personnel activity
        personnel_data = get_data("delivery_personnel") or {}
        active_personnel = len([p for p in personnel_data.values() if p.get('status') == 'available'])
        busy_personnel = len([p for p in personnel_data.values() if p.get('status') == 'busy'])

        text = f"""
📅 **Daily Analytics - {today}**

**Order Summary:**
• Total Orders: {total_orders}
• Completed: {completed_orders}
• Pending: {pending_orders}
• Success Rate: {(completed_orders/total_orders*100) if total_orders > 0 else 0:.1f}%

**Revenue:**
• Total Revenue: {total_revenue:.2f} birr
• Average Order: {(total_revenue/completed_orders) if completed_orders > 0 else 0:.2f} birr

**Personnel Status:**
• Active: {active_personnel}
• Busy: {busy_personnel}
• Total: {len(personnel_data)}

**Performance:**
• Orders per Personnel: {(completed_orders/len(personnel_data)) if len(personnel_data) > 0 else 0:.1f}
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_daily"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error showing daily analytics: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_weekly_analytics(call):
    """Show weekly analytics summary"""
    try:
        # Calculate week range
        today = datetime.datetime.now()
        week_start = today - datetime.timedelta(days=today.weekday())
        week_end = week_start + datetime.timedelta(days=6)

        week_range = f"{week_start.strftime('%Y-%m-%d')} to {week_end.strftime('%Y-%m-%d')}"

        # Get order data for this week
        orders_data = get_data("orders") or {}
        week_orders = []

        for order in orders_data.values():
            order_date = order.get('timestamp', '')
            if order_date:
                try:
                    order_datetime = datetime.datetime.fromisoformat(order_date.replace('Z', '+00:00'))
                    if week_start <= order_datetime <= week_end:
                        week_orders.append(order)
                except:
                    continue

        # Calculate metrics
        total_orders = len(week_orders)
        completed_orders = len([o for o in week_orders if o.get('status') == 'completed'])
        total_revenue = sum(
            float(order.get('total_amount', 0)) for order in week_orders
            if order.get('status') == 'completed'
        )

        # Daily breakdown
        daily_counts = {}
        for i in range(7):
            day = week_start + datetime.timedelta(days=i)
            day_str = day.strftime('%Y-%m-%d')
            daily_counts[day.strftime('%a')] = len([
                o for o in week_orders
                if o.get('timestamp', '').startswith(day_str)
            ])

        daily_breakdown = '\n'.join([f"• {day}: {count}" for day, count in daily_counts.items()])

        text = f"""
📊 **Weekly Analytics**
**Period:** {week_range}

**Summary:**
• Total Orders: {total_orders}
• Completed: {completed_orders}
• Total Revenue: {total_revenue:.2f} birr
• Daily Average: {(total_orders/7):.1f} orders

**Daily Breakdown:**
{daily_breakdown}

**Performance:**
• Completion Rate: {(completed_orders/total_orders*100) if total_orders > 0 else 0:.1f}%
• Average Revenue/Day: {(total_revenue/7):.2f} birr
        """

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_weekly"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error showing weekly analytics: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_monthly_analytics(call):
    """Show monthly analytics summary"""
    management_bot.answer_callback_query(call.id, "📈 Monthly analytics coming soon!")

def show_transaction_analytics(call):
    """Show transaction count analytics"""
    management_bot.answer_callback_query(call.id, "🔢 Transaction analytics coming soon!")

def show_delivery_analytics(call):
    """Show delivery performance analytics"""
    try:
        # Get delivery personnel performance data
        personnel_data = get_data("delivery_personnel") or {}
        performance_data = get_data("delivery_personnel_performance") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}

        if not personnel_data:
            text = "❌ **No Delivery Data**\n\nNo delivery personnel found in the system."
        else:
            text = "🚚 **Delivery Analytics**\n\n"

            # Overall stats
            total_personnel = len(personnel_data)
            active_personnel = len([p for p in personnel_data.values() if p.get('status') == 'available'])
            busy_personnel = len([p for p in personnel_data.values() if p.get('status') == 'busy'])

            text += f"""**Personnel Overview:**
• Total Personnel: {total_personnel}
• Currently Active: {active_personnel}
• Currently Busy: {busy_personnel}
• Offline: {total_personnel - active_personnel - busy_personnel}

**Top Performers:**
            """

            # Get top performers (simplified)
            performer_stats = []
            for personnel_id, person in personnel_data.items():
                name = person.get('name', 'Unknown')
                perf = performance_data.get(personnel_id, {})
                completed = perf.get('completed_deliveries', 0)
                performer_stats.append((name, completed))

            # Sort by completed deliveries
            performer_stats.sort(key=lambda x: x[1], reverse=True)

            for i, (name, completed) in enumerate(performer_stats[:5], 1):
                text += f"\n{i}. {name}: {completed} deliveries"

            # Current assignments
            active_assignments = len([a for a in assignments_data.values() if a.get('status') == 'active'])
            text += f"\n\n**Current Status:**\n• Active Assignments: {active_assignments}"

        keyboard = types.InlineKeyboardMarkup()
        keyboard.add(
            types.InlineKeyboardButton("🔄 Refresh", callback_data="analytics_delivery"),
            types.InlineKeyboardButton("🔙 Back", callback_data="mgmt_analytics")
        )

        management_bot.edit_message_text(
            text,
            call.message.chat.id,
            call.message.message_id,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"Error showing delivery analytics: {e}")
        management_bot.answer_callback_query(call.id, f"❌ Error: {str(e)}", show_alert=True)

def show_trend_analytics(call):
    """Show trend analysis"""
    management_bot.answer_callback_query(call.id, "💹 Trend analysis coming soon!")

def register_management_bot_handlers():
    """Register all management bot handlers"""
    try:
        # Clear any existing handlers first
        management_bot.message_handlers.clear()
        management_bot.callback_query_handlers.clear()

        # Register start and help command handler
        management_bot.register_message_handler(handle_start, commands=['start', 'help'])

        # Register callback query handler
        management_bot.register_callback_query_handler(handle_callback_query, func=lambda call: True)

        # Register text message handlers for personnel addition (with lower priority)
        management_bot.register_message_handler(process_add_personnel, func=lambda message: message.content_type == 'text' and not message.text.startswith('/'))

        logger.info("Management bot handlers registered successfully")
        logger.info(f"Registered {len(management_bot.message_handlers)} message handlers")
        logger.info(f"Registered {len(management_bot.callback_query_handlers)} callback handlers")

    except Exception as e:
        logger.error(f"Error registering management bot handlers: {e}")
        raise

def run_management_bot():
    """Run the management bot"""
    logger.info("Starting Management Bot...")
    try:
        # Register handlers first
        register_management_bot_handlers()

        # Start polling
        management_bot.polling(none_stop=True, interval=1)
    except Exception as e:
        logger.error(f"Management Bot error: {e}")
        raise

if __name__ == "__main__":
    run_management_bot()
