#!/usr/bin/env python3
"""
Simple Management Bot Test
Direct test of management bot functionality
"""

import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_management_bot():
    """Simple test of management bot"""
    print("🧪 SIMPLE MANAGEMENT BOT TEST")
    print("=" * 40)
    
    try:
        # Import required modules
        from src.bots.management_bot import management_bot, register_management_bot_handlers
        
        print("✅ Management bot imported")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot: @{bot_info.username}")
        
        # Register handlers
        register_management_bot_handlers()
        print("✅ Handlers registered")
        
        # Create a simple message handler test
        @management_bot.message_handler(commands=['test'])
        def test_handler(message):
            management_bot.reply_to(message, "✅ Management bot is working!")
        
        print("\n🚀 Bot is ready!")
        print("Send /start or /test to @Wiz_Aroma_Finance_bot")
        print("Press Ctrl+C to stop...")
        
        # Start polling
        management_bot.infinity_polling()
        
    except KeyboardInterrupt:
        print("\n⏹️ Bot stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_management_bot()
